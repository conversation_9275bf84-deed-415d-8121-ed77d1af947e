'use client';

import { useState, useEffect, useRef } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Trash2, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  TableCell,
  TableRow,
} from '@/components/ui/table';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { ProductSelector } from '@/components/ui/product-selector';
import { UnitSelectorWithStock } from '@/components/ui/unit-selector-with-stock';
import { DiscountTypeSelector } from '@/components/ui/discount-type-selector';
import { LiveCurrencyInput } from '@/components/ui/currency-input';
import { useProduct } from '@/hooks/useProducts';
import { formatCurrency } from '@/lib/utils';

interface SaleItemsTableProps {
  form: UseFormReturn<any>;
  itemFields: any[];
  onAddItem: () => void;
  onRemoveItem: (index: number) => void;
}

export function SaleItemsTable({
  form,
  itemFields,
  onAddItem,
  onRemoveItem,
}: SaleItemsTableProps) {
  const headerScrollRef = useRef<HTMLDivElement>(null);
  const scrollAreaViewportRef = useRef<HTMLDivElement>(null);


  // Synchronize horizontal scroll between header and ScrollArea content
  const handleHeaderScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (scrollAreaViewportRef.current) {
      scrollAreaViewportRef.current.scrollLeft = e.currentTarget.scrollLeft;
    }
  };

  const handleContentScroll = (e: Event) => {
    const target = e.target as HTMLDivElement;
    if (headerScrollRef.current && target) {
      headerScrollRef.current.scrollLeft = target.scrollLeft;
    }
  };

  // Set up scroll synchronization with ScrollArea viewport
  useEffect(() => {
    const viewport = scrollAreaViewportRef.current;
    if (viewport) {
      viewport.addEventListener('scroll', handleContentScroll);
      return () => viewport.removeEventListener('scroll', handleContentScroll);
    }
  }, []);

  if (itemFields.length === 0) {
    return (
      <div className="text-center py-12 text-muted-foreground bg-muted/5">
        <div className="flex flex-col items-center gap-4">
          <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
            <Plus className="h-8 w-8 text-primary/60" />
          </div>
          <div>
            <p className="text-base font-medium text-foreground">Mulai Transaksi</p>
            <p className="text-sm">Tambahkan item pertama untuk memulai transaksi penjualan</p>
          </div>
          <Button onClick={(e) => {
            e.preventDefault();
            onAddItem();
          }} className="mt-2">
            <Plus className="h-4 w-4 mr-2" />
            Tambah Item Pertama
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="relative">
        {/* Mobile: Show hint about horizontal scroll */}
        <div className="block sm:hidden bg-amber-50/50 border-b sticky top-0 z-40">
          <div className="text-xs text-amber-600 px-3 py-1.5 text-center">
            📱 Geser tabel ke kanan untuk melihat semua kolom
          </div>
        </div>

        {/* Sticky Header Row - Using table structure for exact width matching */}
        <div className="sticky top-0 z-30 bg-background border-b">
          <div
            ref={headerScrollRef}
            className="w-full overflow-x-auto scroll-px-0 no-scrollbar"
            onScroll={handleHeaderScroll}
          >
            <div className="flex">
              {/* Main header content */}
              <div className="flex-1 pr-[40px]">
                <table className="w-full min-w-[1160px] table-fixed caption-bottom text-sm">
                  <colgroup>
                    <col className="w-[160px]" />
                    <col className="w-[120px]" />
                    <col className="w-[70px]" />
                    <col className="w-[90px]" />
                    <col className="w-[60px]" />
                    <col className="w-[80px]" />
                    <col className="w-[90px]" />
                  </colgroup>
                  <thead>
                    <tr className="border-b bg-gradient-to-r from-muted/40 to-muted/20">
                      <th className="h-8 px-3 py-2 text-left align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Produk</th>
                      <th className="h-8 px-2 py-2 text-left align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Satuan</th>
                      <th className="h-8 px-2 py-2 text-center align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Qty</th>
                      <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Harga</th>
                      <th className="h-8 px-2 py-2 text-center align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Disc</th>
                      <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Nilai</th>
                      <th className="h-8 px-2 py-2 text-right align-middle whitespace-nowrap text-xs font-semibold text-muted-foreground bg-muted/40">Subtotal</th>
                    </tr>
                  </thead>
                </table>
              </div>

              {/* Sticky action header */}
              <div className="w-[40px] bg-muted/40 border-l border-b">
                <div className="h-8 flex items-center justify-center">
                  <span className="text-xs font-semibold text-muted-foreground">×</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Table Content with ScrollArea - Split layout for sticky positioning */}
        <div className="relative w-full h-[400px] border rounded-md overflow-hidden">
          {/* Main table content in ScrollArea */}
          <ScrollArea
            className="w-full h-full"
            ref={(el) => {
              if (el) {
                const viewport = el.querySelector('[data-slot="scroll-area-viewport"]') as HTMLDivElement;
                scrollAreaViewportRef.current = viewport;
              }
            }}
          >
            <div className="w-full pr-[40px]"> {/* Reserve space for sticky column */}
              <table className="w-full min-w-[1160px] table-fixed caption-bottom text-sm">
                <colgroup>
                  <col className="w-[160px]" />
                  <col className="w-[120px]" />
                  <col className="w-[70px]" />
                  <col className="w-[90px]" />
                  <col className="w-[60px]" />
                  <col className="w-[80px]" />
                  <col className="w-[90px]" />
                  {/* No action column here */}
                </colgroup>
                <tbody className="[&_tr:last-child]:border-0">
                  {itemFields.map((field, index) => (
                    <SaleItemRowContent
                      key={field.id}
                      index={index}
                      form={form}
                    />
                  ))}
                </tbody>
              </table>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>

          {/* Sticky action column overlay - Table structure for perfect alignment */}
          <div className="absolute top-0 right-0 w-[40px] h-full bg-background border-l z-10">
            <table className="w-full h-full table-fixed caption-bottom text-sm">
              <colgroup>
                <col className="w-[40px]" />
              </colgroup>
              <tbody className="[&_tr:last-child]:border-0">
                {itemFields.map((field, index) => (
                  <tr key={field.id} className="border-b">
                    <td className="px-1 py-1 text-center align-middle">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => onRemoveItem(index)}
                        className="text-destructive hover:text-destructive h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {/* Add Item Button */}
      <div className="border-t bg-muted/5 px-4 py-3">
        <Button
          onClick={(e) => {
            e.preventDefault();
            onAddItem();
          }}
          size="sm"
          variant="secondary"
          className="w-full"
        >
          <Plus className="h-3 w-3 mr-2" />
          Tambah Item Lainnya
        </Button>
      </div>
    </>
  );
}

// Action button component that matches table row height
function SaleItemActionButton({
  index,
  onRemove,
}: {
  index: number;
  onRemove: () => void;
}) {
  const [height, setHeight] = useState<number>(48); // Default height to match typical table row

  useEffect(() => {
    // Function to update height based on corresponding table row
    const updateHeight = () => {
      const tableRow = document.querySelector(`[data-row-index="${index}"]`) as HTMLElement;
      if (tableRow) {
        // Use getBoundingClientRect for more accurate measurements
        const rect = tableRow.getBoundingClientRect();
        const rowHeight = Math.round(rect.height);
        setHeight(rowHeight);
      }
    };

    // Multiple attempts to ensure accurate height calculation
    const timers = [
      setTimeout(updateHeight, 50),   // Quick initial check
      setTimeout(updateHeight, 200),  // After potential layout shifts
      setTimeout(updateHeight, 500),  // After all animations/transitions
    ];

    // Set up a ResizeObserver for more accurate size tracking
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target.getAttribute('data-row-index') === index.toString()) {
          const height = Math.round(entry.contentRect.height);
          setHeight(height);
        }
      }
    });

    // Set up a MutationObserver to watch for changes in the table row
    const mutationObserver = new MutationObserver(() => {
      // Delay to allow layout to settle
      setTimeout(updateHeight, 10);
    });

    const tableRow = document.querySelector(`[data-row-index="${index}"]`);

    if (tableRow) {
      resizeObserver.observe(tableRow);
      mutationObserver.observe(tableRow, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['class', 'style']
      });
    }

    // Also listen for window resize
    window.addEventListener('resize', updateHeight);

    return () => {
      timers.forEach(clearTimeout);
      resizeObserver.disconnect();
      mutationObserver.disconnect();
      window.removeEventListener('resize', updateHeight);
    };
  }, [index]);

  return (
    <div
      className="flex items-center justify-center border-b last:border-b-0 transition-all duration-150 relative"
      style={{
        height: `${height}px`,
        minHeight: `${height}px`,
        maxHeight: `${height}px`
      }}
    >
      <Button
        type="button"
        variant="ghost"
        size="sm"
        onClick={onRemove}
        className="text-destructive hover:text-destructive h-6 w-6 p-0 shrink-0"
      >
        <Trash2 className="h-3 w-3" />
      </Button>
    </div>
  );
}

// Individual Sale Item Row Content (without action column)
function SaleItemRowContent({
  index,
  form,
}: {
  index: number;
  form: UseFormReturn<any>;
}) {
  const [availableUnits, setAvailableUnits] = useState<any[]>([]);

  // Watch form values
  const watchedProductId = form.watch(`items.${index}.productId`);
  const watchedUnitId = form.watch(`items.${index}.unitId`);
  const watchedQuantity = form.watch(`items.${index}.quantity`);
  const watchedUnitPrice = form.watch(`items.${index}.unitPrice`);
  const watchedDiscountType = form.watch(`items.${index}.discountType`);
  const watchedDiscountValue = form.watch(`items.${index}.discountValue`);

  // Fetch product details
  const { data: productData } = useProduct(watchedProductId);

  // Update available units when product changes
  useEffect(() => {
    if (productData?.unitHierarchies) {
      const units = productData.unitHierarchies.map((hierarchy: any) => ({
        id: hierarchy.unitId,
        name: hierarchy.unit.name,
        abbreviation: hierarchy.unit.abbreviation,
        conversionFactor: hierarchy.conversionFactor,
        sellingPrice: hierarchy.sellingPrice,
        level: hierarchy.level,
        isBaseUnit: hierarchy.level === 0,
        // TODO: Add stock information integration
        stockInfo: {
          quantityOnHand: 100, // Mock data - replace with real stock data
          quantityAllocated: 10,
          availableStock: 90,
        },
      }));

      setAvailableUnits(units);

      // Auto-select base unit if no unit selected
      if (watchedProductId && !watchedUnitId) {
        const baseUnit = units.find((unit: any) => unit.isBaseUnit);
        if (baseUnit) {
          form.setValue(`items.${index}.unitId`, baseUnit.id);
          form.setValue(`items.${index}.unitPrice`, baseUnit.sellingPrice || 0);
        }
      }
    }
  }, [productData, watchedProductId, watchedUnitId, form, index]);

  // Product data is available through productData variable

  // Update unit price when unit changes
  useEffect(() => {
    if (watchedUnitId && availableUnits.length > 0) {
      const selectedUnit = availableUnits.find((unit: any) => unit.id === watchedUnitId);
      if (selectedUnit?.sellingPrice) {
        form.setValue(`items.${index}.unitPrice`, selectedUnit.sellingPrice);
      }
    }
  }, [watchedUnitId, availableUnits, form, index]);

  // Calculate totals
  const itemSubtotal = (watchedQuantity || 0) * (watchedUnitPrice || 0);
  const itemDiscount = watchedDiscountType === 'PERCENTAGE'
    ? itemSubtotal * ((watchedDiscountValue || 0) / 100)
    : (watchedDiscountValue || 0);
  const itemTotal = itemSubtotal - itemDiscount;

  return (
    <TableRow
      className="hover:bg-muted/20"
      data-row-index={index}
    >
      {/* Product */}
      <TableCell className="px-2 py-2 max-w-0 overflow-hidden">
        <FormField
          control={form.control}
          name={`items.${index}.productId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <ProductSelector
                  value={field.value}
                  onValueChange={(value) => {
                    field.onChange(value);
                    form.setValue(`items.${index}.unitId`, '');
                    form.setValue(`items.${index}.unitPrice`, 0);
                  }}
                  placeholder="Pilih produk..."
                  className="h-8 text-xs w-full min-w-0"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Unit */}
      <TableCell className="px-2 py-1 max-w-0 overflow-hidden">
        <FormField
          control={form.control}
          name={`items.${index}.unitId`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <UnitSelectorWithStock
                  units={availableUnits}
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="Satuan..."
                  disabled={!watchedProductId}
                  showStockInfo={true}
                  className="h-8 text-xs w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Quantity */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.quantity`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...form.register(`items.${index}.quantity`)}
                  type="number"
                  min="1"
                  step="1"
                  placeholder='1'
                  defaultValue={field.value || 1}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                  className="h-8 text-xs text-center px-2 w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Unit Price */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.unitPrice`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <LiveCurrencyInput
                  value={field.value}
                  onValueChange={field.onChange}
                  placeholder="0"
                  className="h-8 text-xs text-right px-2 w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Discount Type */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.discountType`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <DiscountTypeSelector
                  value={field.value}
                  onValueChange={field.onChange}
                  size="sm"
                  className="justify-center w-full"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Discount Value */}
      <TableCell className="px-2 py-1 max-w-0">
        <FormField
          control={form.control}
          name={`items.${index}.discountValue`}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input
                  {...form.register(`items.${index}.discountValue`)}
                  type="number"
                  min="0"
                  step="0.01"
                  defaultValue={field.value || 0}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                  disabled={!watchedDiscountType}
                  className="h-8 text-xs text-right px-2 w-full"
                  placeholder="0"
                />
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          )}
        />
      </TableCell>

      {/* Subtotal */}
      <TableCell className="px-2 py-1 max-w-0">
        <div className="text-right overflow-hidden">
          <div className="font-semibold text-sn text-primary truncate">
            {formatCurrency(itemTotal)}
          </div>
          {itemDiscount > 0 && (
            <div className="text-[10px] text-red-500 truncate">
              -{formatCurrency(itemDiscount)}
            </div>
          )}
        </div>
      </TableCell>

    </TableRow>
  );
}
